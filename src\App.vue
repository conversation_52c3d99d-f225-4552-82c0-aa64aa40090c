<template>
  <div id="app">
    <el-container>
      <el-header class="header">
        <div class="header-content">
          <h1 class="title">
            <el-icon><TrendCharts /></el-icon>
            加密货币交易所监控
          </h1>
          <el-menu
            :default-active="$route.path"
            class="nav-menu"
            mode="horizontal"
            router
          >
            <el-menu-item index="/exchanges">
              <el-icon><Shop /></el-icon>
              交易所排行
            </el-menu-item>
            <el-menu-item index="/global">
              <el-icon><DataAnalysis /></el-icon>
              全市场分析
            </el-menu-item>
          </el-menu>
        </div>
      </el-header>
      
      <el-main class="main-content">
        <router-view />
      </el-main>
      
      <el-footer class="footer">
        <p>© 2024 Crypto Exchange Monitor - 实时加密货币交易数据监控</p>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { TrendCharts, Shop, DataAnalysis } from '@element-plus/icons-vue'
import ccxt from 'ccxt'
console.log(ccxt)
</script>

<style scoped>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 80px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-menu {
  background: transparent;
  border: none;
}

.nav-menu .el-menu-item {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 2px solid transparent;
}

.nav-menu .el-menu-item:hover,
.nav-menu .el-menu-item.is-active {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  border-bottom-color: white;
}

.main-content {
  min-height: calc(100vh - 140px);
  background: #f5f7fa;
  padding: 20px;
}

.footer {
  background: #2c3e50;
  color: #bdc3c7;
  text-align: center;
  height: 60px !important;
  line-height: 60px;
  padding: 0;
}

.footer p {
  margin: 0;
  font-size: 14px;
}
</style>
